// Test the specific scenario from the bug report

// Simulate the flexible replacement logic from smart-edit.ts
function simulateFlexibleReplacement(content, search, replace) {
    console.log("=== Simulating flexible replacement logic ===");
    console.log("Content:", JSON.stringify(content));
    console.log("Search:", JSON.stringify(search));
    console.log("Replace:", JSON.stringify(replace));
    
    // This is the logic from calculateFlexibleReplacement
    const sourceLines = content.match(/.*(?:\n|$)/g)?.slice(0, -1) ?? [];
    console.log("\nSource lines:", sourceLines.map(line => JSON.stringify(line)));
    
    const searchLinesStripped = search.split('\n').map(line => line.trim());
    console.log("Search lines stripped:", searchLinesStripped);
    
    const replaceLines = replace.split('\n');
    console.log("Replace lines:", replaceLines);
    
    let i = 0;
    while (i <= sourceLines.length - searchLinesStripped.length) {
        const window = sourceLines.slice(i, i + searchLinesStripped.length);
        const windowStripped = window.map(line => line.trim());
        const isMatch = windowStripped.every((line, index) => line === searchLinesStripped[index]);
        
        if (isMatch) {
            console.log(`\nMatch found at position ${i}`);
            console.log("Window:", window.map(line => JSON.stringify(line)));
            console.log("Window stripped:", windowStripped);
            
            const firstLineInMatch = window[0];
            const indentationMatch = firstLineInMatch.match(/^(\s*)/);
            const indentation = indentationMatch ? indentationMatch[1] : '';
            console.log("Detected indentation:", JSON.stringify(indentation));
            
            const newBlockWithIndent = replaceLines.map(line => `${indentation}${line}`);
            console.log("New block with indent:", newBlockWithIndent);
            
            // This is where the bug might be - replacing multiple lines with single joined line
            console.log(`\nReplacing ${searchLinesStripped.length} lines with 1 joined line`);
            sourceLines.splice(i, searchLinesStripped.length, newBlockWithIndent.join('\n'));
            console.log("Source lines after splice:", sourceLines.map(line => JSON.stringify(line)));
            
            break;
        }
        i++;
    }
    
    const result = sourceLines.join('');
    console.log("\nFinal result:", JSON.stringify(result));
    console.log("Original length:", content.length);
    console.log("Result length:", result.length);
    console.log("Character difference:", content.length - result.length);
    
    return result;
}

// Test case based on the bug report
const originalContent = `       // setting envDir to the monorepo root (and then exempting cached files),
       // we ensure that all our packages are watched.`;

const searchPattern = `       // setting envDir to the monorepo root (and then exempting cached files),`;

const replacePattern = `       // setting envDir to the monopo root (and then exempting cached files),`;

simulateFlexibleReplacement(originalContent, searchPattern, replacePattern);