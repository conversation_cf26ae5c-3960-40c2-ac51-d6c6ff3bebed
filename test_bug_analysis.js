// This reproduces the bug from the calculateFlexibleReplacement function

const originalContent = `        // setting envDir to the monorepo root (and then exempting cached files),
        // we ensure that all our packages are watched.
        envDir: __dirname,`;

console.log("Original content:");
console.log(originalContent);
console.log("\nProcessing with the buggy regex:");

// This is the problematic line from smart-edit.ts:118
const sourceLines = originalContent.match(/.*(?:\n|$)/g)?.slice(0, -1) ?? [];
console.log("Source lines array:", sourceLines);

console.log("\nJoining back:");
const joined = sourceLines.join('');
console.log("Joined result:");
console.log(joined);

console.log("\nComparison:");
console.log("Original length:", originalContent.length);
console.log("Joined length:", joined.length);
console.log("Characters lost:", originalContent.length - joined.length);