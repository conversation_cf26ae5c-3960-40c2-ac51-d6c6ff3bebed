// Test to reproduce the character loss bug in unescapeStringForGeminiBug

function unescapeStringForGeminiBug(inputString) {
  return inputString.replace(
    /\\+(n|t|r|'|"|`|\\|\n)/g,
    (match, capturedChar) => {
      switch (capturedChar) {
        case 'n':
          return '\n'; // This is the problem!
        case 't':
          return '\t';
        case 'r':
          return '\r';
        case "'":
          return "'";
        case '"':
          return '"';
        case '`':
          return '`';
        case '\\':
          return '\\';
        case '\n':
          return '\n';
        default:
          return match;
      }
    },
  );
}

// Test cases that demonstrate the bug
console.log('Testing character loss bug:');

// Test 1: "monorepo" -> "monopo" (the 'r' gets lost)
const test1 = 'monorepo root';
console.log(`Input: "${test1}"`);
console.log(`Output: "${unescapeStringForGeminiBug(test1)}"`);
console.log('Expected: Same as input (no change)');
console.log('');

// Test 2: Path with \\name -> \name (newline character)
const test2 = 'C:\\\\Users\\\\<USER>